<?php

namespace App\Http\Controllers\Api\Player;

use App\Http\Controllers\Controller;
use App\Models\Game;
use App\Models\PlayerStat;
use App\Models\StatsParameter;
use App\Services\TimezoneService;
use Illuminate\Support\Facades\Auth;

class StatsController extends Controller
{
    protected $timezoneService;

    public function __construct(TimezoneService $timezoneService)
    {
        $this->timezoneService = $timezoneService;
    }

    /**
     * Get stats for the authenticated player from last 7 games
     * Client clarification: Last 7 games (not 7 days), determine total games first, then show last 7
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPlayerStats()
    {
        $player = Auth::guard('player')->user();

        if (!$player) {
            return jsonResponse(false, ['message' => 'Player not authenticated']);
        }

        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        // First, determine how many games in total this player has in the database
        $totalPlayerGames = PlayerStat::where('player_id', $player->player_id)
            ->distinct()
            ->count('game_id');

        if ($totalPlayerGames === 0) {
            return jsonResponse(true, [
                'stats' => [],
                'overall_rating' => 0,
                'daily_ratings' => [],
                'total_games' => 0
            ]);
        }

        // Get all games for this player, ordered by date/time (most recent first)
        $playerGameIds = PlayerStat::where('player_id', $player->player_id)
            ->distinct()
            ->pluck('game_id')
            ->toArray();

        $games = Game::whereIn('id', $playerGameIds)
            ->orderBy('date', 'desc')
            ->orderBy('end_time', 'desc')
            ->get();

        // Filter ended games and take only last 7 (or less if player has fewer than 7 games)
        $endedGameIds = [];
        foreach ($games as $game) {
            if ($this->timezoneService->hasGameEnded($game, $userTimezone)) {
                $endedGameIds[] = $game->id;
                if (count($endedGameIds) >= 7) {
                    break; // Only take last 7 games
                }
            }
        }

        if (empty($endedGameIds)) {
            return jsonResponse(true, [
                'stats' => [],
                'overall_rating' => 0,
                'daily_ratings' => [],
                'total_games' => $totalPlayerGames
            ]);
        }

        // Get stats for last 7 ended games - only player-specific stats
        $playerStats = PlayerStat::with(['parameter', 'game'])
            ->whereIn('game_id', $endedGameIds)
            ->where('player_id', $player->player_id)
            ->get();

        if (!$playerStats->count()) {
            return jsonResponse(true, [
                'stats' => [],
                'overall_rating' => 0,
                'daily_ratings' => [],
                'total_games' => $totalPlayerGames
            ]);
        }

        // Get all stats parameters
        $allStatsParameters = StatsParameter::all();

        // Calculate daily ratings for the graph (last 7 games, not days)
        // Always show exactly 7 games, set missing ratings to 0
        $dailyRatings = [];
        $ratingParamId = 23; // RATING type ID

        // Reverse the ended games to get the oldest game first
        // $endedGameIds = array_reverse($endedGameIds);

        // Group stats by game for rating calculation
        $statsByGame = $playerStats->groupBy('game_id');

        // Loop over days 1 to 7 from oldest to latest
        for ($day = 1; $day <= 7; $day++) {
            $index = 7 - $day;
            $ratingForGame = 0;

            // Check if we have a game at this index
            if (isset($endedGameIds[$index])) {
                $gameId = $endedGameIds[$index];
                $gameStats = $statsByGame->get($gameId, collect());

                // Find rating stat for this game
                $ratingStat = $gameStats->where('stat_param_id', $ratingParamId)->first();
                if ($ratingStat) {
                    $ratingForGame = $ratingStat->total;
                }
            }

            $dailyRatings[] = [
                'day' => $day,
                'rating' => round($ratingForGame, 2)
            ];
        }



        // Calculate overall rating from last 7 games
        $overallRating = 0;
        $ratingStats = $playerStats->where('stat_param_id', $ratingParamId);
        if ($ratingStats->count() > 0) {
            $overallRating = $ratingStats->avg('total');
        }

        // Format stats for response - include all parameters, set missing to 0
        $formattedStats = [];
        foreach ($allStatsParameters as $parameter) {
            if ($parameter->id == 23) {
                continue; // Skip RATING type
            }
            $paramStats = $playerStats->where('stat_param_id', $parameter->id);

            if ($paramStats->count() > 0) {
                // Sum or average based on sum_avg field
                if ($parameter->sum_avg == 1) {
                    $total = $paramStats->sum('total');
                    $success = $paramStats->sum('success');
                    $winner = $paramStats->sum('winner');
                    $loss = $paramStats->sum('loss');
                } else {
                    $total = $paramStats->avg('total');
                    $success = $paramStats->avg('success');
                    $winner = $paramStats->avg('winner');
                    $loss = $paramStats->avg('loss');
                }


                $formattedStats[] = [
                    'id' => $parameter->id,
                    'total' => round($total, 2),
                    'success' => round($success, 2),
                    'winner' => round($winner, 2),
                    'loss' => round($loss, 2),
                    'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0,
                    'label' => $parameter->label,
                    'sum_avg' => $parameter->sum_avg
                ];
            }
        }


        return jsonResponse(true, [
            'stats' => $formattedStats,
            'overall_rating' => round($overallRating, 2),
            'daily_ratings' => $dailyRatings,
            'total_games' => $totalPlayerGames,
            'games_shown' => count($endedGameIds)
        ]);
    }

    public function getGamesStats()
    {
        $player = Auth::guard('player')->user();

        if (!$player) {
            return jsonResponse(false, ['message' => 'Player not authenticated']);
        }

        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        $games = Game::orderBy('date', 'desc')
            ->orderBy('end_time', 'desc')
            ->get();

        $endedGameIds = [];
        foreach ($games as $game) {
            if ($this->timezoneService->hasGameEnded($game, $userTimezone)) {
                $endedGameIds[] = $game->id;
            }
        }

        if (empty($endedGameIds)) {
            return jsonResponse(true, [
                'game_id' => null,
                'stats' => [],
            ]);
        }

        $playerStats = PlayerStat::with(['parameter', 'game'])
            ->whereIn('game_id', $endedGameIds)
            ->whereNull('player_id')
            ->get();

        if (!$playerStats->count()) {
            return jsonResponse(true, [
                'stats' => [],
                'total_games' => 0
            ]);
        }

        // Get all stats parameters
        $allStatsParameters = StatsParameter::all();



        // Format stats for response - include all parameters, set missing to 0
        $formattedStats = [];
        foreach ($allStatsParameters as $parameter) {
            if ($parameter->id == 23) {
                continue; // Skip RATING type
            }
            $paramStats = $playerStats->where('stat_param_id', $parameter->id);

            if ($paramStats->count() > 0) {
                // Sum or average based on sum_avg field
                if ($parameter->sum_avg == 1) {
                    $total = $paramStats->sum('total');
                    $success = $paramStats->sum('success');
                    $winner = $paramStats->sum('winner');
                    $loss = $paramStats->sum('loss');
                } else {
                    $total = $paramStats->avg('total');
                    $success = $paramStats->avg('success');
                    $winner = $paramStats->avg('winner');
                    $loss = $paramStats->avg('loss');
                }


                $formattedStats[] = [
                    'id' => $parameter->id,
                    'total' => round($total, 2),
                    'success' => round($success, 2),
                    'winner' => round($winner, 2),
                    'loss' => round($loss, 2),
                    'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0,
                    'label' => $parameter->label,
                    'sum_avg' => $parameter->sum_avg
                ];
            }
        }


        return jsonResponse(true, [
            'game_id' => 0,
            'stats' => $formattedStats,
            'games_shown' => count($endedGameIds)
        ]);
    }


    /**
     * Get last game stats for authenticated player
     * Client clarification: Show last game's record stats, and also check all players stats
     * for the same day, same camera, same timeframes
     */
    public function getLastGameStats()
    {
        $player = Auth::guard('player')->user();

        if (!$player) {
            return jsonResponse(false, ['message' => 'Player not authenticated']);
        }

        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        // Get all games for this player and filter ended games
        $playerGameIds = PlayerStat::where('player_id', $player->player_id)
            ->distinct()
            ->pluck('game_id');

        $games = Game::whereIn('id', $playerGameIds)->orderBy('date', 'desc')->orderBy('end_time', 'desc')->get();

        // Find the latest ended game
        $latestGame = null;
        foreach ($games as $game) {
            if ($this->timezoneService->hasGameEnded($game, $userTimezone)) {
                $latestGame = $game;
                break;
            }
        }

        // Get all stats parameters
        $allStatsParameters = StatsParameter::all();

        if (!$latestGame) {
            return jsonResponse(true, [
                'stats' => [],
                'all_players_stats' => []
            ]);
        }

        // Get stats for the latest game for this player
        $lastGameStats = PlayerStat::with(['parameter', 'game'])
            ->where('player_id', $player->player_id)
            ->where('game_id', $latestGame->id)
            ->get();

        // Client clarification: Check all players stats for same day, same camera, same timeframes
        $allPlayersStats = PlayerStat::with(['parameter', 'player'])
            ->whereHas('game', function ($query) use ($latestGame) {
                $query->where('date', $latestGame->date)
                    ->where('camera_id', $latestGame->camera_id)
                    ->where('start_time', $latestGame->start_time)
                    ->where('end_time', $latestGame->end_time);
            })
            ->whereNotNull('player_id') // Only player-specific stats, not game-level stats
            ->get();

        // Format player's stats for response - include all parameters, set missing to 0
        $formattedStats = [];
        foreach ($allStatsParameters as $parameter) {
            if ($parameter->id == 23) {
                continue; // Skip RATING type
            }
            $paramStat = $lastGameStats->where('stat_param_id', $parameter->id)->first();

            if ($paramStat) {
                $total = $paramStat->total;
                $success = $paramStat->success;
                $winner = $paramStat->winner;
                $loss = $paramStat->loss;

                $formattedStats[] = [
                    'id' => $parameter->id,
                    'total' => round($total, 2),
                    'success' => round($success, 2),
                    'winner' => round($winner, 2),
                    'loss' => round($loss, 2),
                    'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0,
                    'label' => $parameter->label,
                    'sum_avg' => $parameter->sum_avg,
                ];
            }
        }

        // Format all players stats for the same game session
        $allPlayersFormatted = [];
        $playerStatsGrouped = $allPlayersStats->groupBy('player_id');

        foreach ($playerStatsGrouped as $playerId => $playerStats) {
            $playerInfo = $playerStats->first()->player;
            $playerStatsFormatted = [];

            foreach ($allStatsParameters as $parameter) {
                if ($parameter->id == 23) {
                    continue; // Skip RATING type
                }
                $paramStat = $playerStats->where('stat_param_id', $parameter->id)->first();

                if ($paramStat) {
                    $total = $paramStat->total;
                    $success = $paramStat->success;
                    $winner = $paramStat->winner;
                    $loss = $paramStat->loss;

                    $playerStatsFormatted[] = [
                        'id' => $parameter->id,
                        'total' => round($total, 2),
                        'success' => round($success, 2),
                        'winner' => round($winner, 2),
                        'loss' => round($loss, 2),
                        'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0,
                        'label' => $parameter->label,
                        'sum_avg' => $parameter->sum_avg,
                    ];
                }
            }

            $allPlayersFormatted[] = [
                'player_id' => $playerId,
                'player_name' => $playerInfo ? $playerInfo->first_name . ' ' . $playerInfo->last_name : 'Unknown',
                'stats' => $playerStatsFormatted
            ];
        }

        return jsonResponse(true, [
            'game_id' => $latestGame->id,
            'game_date' => $latestGame->date,
            'camera_id' => $latestGame->camera_id,
            'stats' => $formattedStats,
            'all_players_stats' => $allPlayersFormatted
        ]);
    }

    /**
     * Get latest game stats for all players (empty user_id records)
     * Client clarification: Show latest combined stats for all players registered in the app
     * These are game-level stats (empty user_id) that should be shown to all players
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLatestGameStats()
    {
        // Get user timezone based on IP
        $userIP = $this->timezoneService->getUserIP();
        $userTimezone = $this->timezoneService->getUserTimezoneByIP($userIP);

        // Get latest game that has stats with empty user_id (game stats for all players)
        $latestGameWithStats = PlayerStat::whereNull('player_id')
            ->with('game')
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$latestGameWithStats) {
            return jsonResponse(true, [
                'stats' => [],
                'message' => 'No game stats available'
            ]);
        }

        $game = $latestGameWithStats->game;

        // Check if game has ended
        if (!$this->timezoneService->hasGameEnded($game, $userTimezone)) {
            return jsonResponse(false, ['message' => 'Game has not ended yet']);
        }

        // Get all stats parameters
        $allStatsParameters = StatsParameter::all();

        // Get all game stats (empty user_id) for this game
        $gameStats = PlayerStat::with(['parameter'])
            ->where('game_id', $game->id)
            ->whereNull('player_id')
            ->get();

        // Format stats for response - include all parameters, set missing to 0
        $formattedStats = [];
        foreach ($allStatsParameters as $parameter) {
            if ($parameter->id == 23) {
                continue; // Skip RATING type
            }
            $paramStat = $gameStats->where('stat_param_id', $parameter->id)->first();

            if ($paramStat) {
                $total = $paramStat->total;
                $success = $paramStat->success;
                $winner = $paramStat->winner;
                $loss = $paramStat->loss;

                $formattedStats[] = [
                    'id' => $parameter->id,
                    'total' => round($total, 2),
                    'success' => round($success, 2),
                    'winner' => round($winner, 2),
                    'loss' => round($loss, 2),
                    'success_rate' => $total > 0 ? round(($success / $total) * 100, 2) : 0,
                    'label' => $parameter->label,
                    'sum_avg' => $parameter->sum_avg,
                ];
            }
        }

        return jsonResponse(true, [
            'game_id' => $game->id,
            'game_date' => $game->date,
            'camera_id' => $game->camera_id,
            'start_time' => $game->start_time,
            'end_time' => $game->end_time,
            'stats' => $formattedStats,
            'message' => 'Latest game stats for all players'
        ]);
    }
}
