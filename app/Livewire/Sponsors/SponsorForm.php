<?php

namespace App\Livewire\Sponsors;

use App\Models\Sponsor;
use App\Models\Commercial;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class SponsorForm extends Component
{
    use WithFileUploads;

    public $sponsor_id;
    public $company_name;
    public $email;
    public $phone;
    public $contact;
    public $tax_id;
    public $postal_code;
    public $country;
    public $commercial_id;
    public $status = 1;
    public $logo;
    public $existingLogo;

    public $isEdit = false;
    public $sponsorId = null;

    // For searchable country select
    public $countrySearch = '';
    public $selectedCountry = null;
    public $countries = [];
    public $showCountryDropdown = false;

    // For commercial select
    public $commercials = [];

    protected $rules = [
        'company_name' => 'required|string|max:50',
        'email' => 'required|email|max:30',
        'phone' => 'nullable|string|max:15',
        'contact' => 'nullable|string|max:50',
        'tax_id' => 'nullable|string|max:12',
        'postal_code' => 'nullable|string|max:10',
        'country' => 'nullable|string|max:2',
        'commercial_id' => 'required|string|exists:commercials,commercial_id',
        'status' => 'required|integer|in:0,1',
        'logo' => 'nullable|image|max:2048', // 2MB max
    ];

    public function mount($sponsorId = null)
    {
        $this->sponsorId = $sponsorId;
        $this->isEdit = !is_null($sponsorId);

        // Load commercials
        $this->commercials = Commercial::select('commercial_id', 'name')
            ->orderBy('name')
            ->get();

        if ($this->isEdit) {
            $sponsor = Sponsor::findOrFail($sponsorId);
            $this->sponsor_id = $sponsor->sponsor_id;
            $this->company_name = $sponsor->company_name;
            $this->email = $sponsor->email;
            $this->phone = $sponsor->phone;
            $this->contact = $sponsor->contact;
            $this->tax_id = $sponsor->tax_id;
            $this->postal_code = $sponsor->postal_code;
            $this->country = $sponsor->country;
            $this->commercial_id = $sponsor->commercial_id;
            $this->status = $sponsor->status;
            $this->existingLogo = $sponsor->logo;

            // Set country search if country is set
            if ($sponsor->country) {
                $countryInfo = DB::table('countries')
                    ->where('iso2', $sponsor->country)
                    ->first();
                if ($countryInfo) {
                    $this->countrySearch = $countryInfo->name;
                    $this->selectedCountry = $countryInfo;
                }
            }
        }

        // Initial load of countries
        $this->loadCountries();
    }

    /**
     * Load countries based on search term
     */
    public function loadCountries()
    {
        $query = DB::table('countries')
            ->select(['id', 'name', 'iso2'])
            ->orderBy('name');

        if ($this->countrySearch) {
            $query->where('name', 'like', '%' . $this->countrySearch . '%');
        }

        $this->countries = $query->limit(10)->get();
    }

    public function updatedCountrySearch()
    {
        $this->loadCountries();
        $this->showCountryDropdown = true;
    }

    public function selectCountry($countryId)
    {
        $country = collect($this->countries)->firstWhere('id', $countryId);
        if ($country) {
            $this->selectedCountry = $country;
            $this->countrySearch = $country->name;
            $this->country = $country->iso2;
            $this->showCountryDropdown = false;
        }
    }

    public function save()
    {
        // Update validation rules based on edit mode
        if ($this->isEdit) {
            $this->rules['email'] = 'required|email|max:30|unique:sponsors,email,' . $this->sponsor_id . ',sponsor_id';
        } else {
            $this->rules['email'] = 'required|email|max:30|unique:sponsors,email';
        }

        $this->validate();

        if ($this->isEdit) {
            $sponsor = Sponsor::findOrFail($this->sponsor_id);

            $sponsor->company_name = $this->company_name;
            $sponsor->email = $this->email;
            $sponsor->phone = $this->phone;
            $sponsor->contact = $this->contact;
            $sponsor->tax_id = $this->tax_id;
            $sponsor->postal_code = $this->postal_code;
            $sponsor->country = $this->country ?? ($this->selectedCountry ? $this->selectedCountry->iso2 : null);
            $sponsor->commercial_id = $this->commercial_id;
            $sponsor->status = $this->status;

            // Handle logo upload
            if ($this->logo) {
                // Delete old logo if exists
                if ($sponsor->logo && Storage::disk('public')->exists($sponsor->logo)) {
                    Storage::disk('public')->delete($sponsor->logo);
                }
                
                $logoPath = $this->logo->store('sponsors/logos', 'public');
                $sponsor->logo = $logoPath;
            }

            $sponsor->save();

            session()->flash('message', 'Sponsor updated successfully.');
        } else {
            // Generate a unique sponsor ID
            $sponsor_id = Sponsor::generateSponsorId();

            $sponsor = new Sponsor();
            $sponsor->sponsor_id = $sponsor_id;
            $sponsor->company_name = $this->company_name;
            $sponsor->email = $this->email;
            $sponsor->phone = $this->phone;
            $sponsor->contact = $this->contact;
            $sponsor->tax_id = $this->tax_id;
            $sponsor->postal_code = $this->postal_code;
            $sponsor->country = $this->country ?? ($this->selectedCountry ? $this->selectedCountry->iso2 : null);
            $sponsor->commercial_id = $this->commercial_id;
            $sponsor->status = $this->status;

            // Handle logo upload
            if ($this->logo) {
                $logoPath = $this->logo->store('sponsors/logos', 'public');
                $sponsor->logo = $logoPath;
            }

            $sponsor->save();

            session()->flash('message', 'Sponsor created successfully.');
        }

        return redirect()->route('sponsors.index');
    }

    public function render()
    {
        return view('livewire.sponsors.sponsor-form');
    }
}
