<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Stat extends Model
{
    protected $fillable = [
        'nbr_users',
        'nbr_subscribed',
        'nbr_clubs',
        'nbr_countries',
        'nbr_sponsors',
    ];

    /**
     * Increment the number of users
     *
     * @return bool
     */
    public static function incrementUsers()
    {
        $stats = self::firstOrCreate(['id' => 1], [
            'nbr_users' => 0,
            'nbr_subscribed' => 0,
            'nbr_clubs' => 0,
            'nbr_countries' => 0,
            'nbr_sponsors' => 0,
        ]);
        
        $stats->increment('nbr_users');
        
        return true;
    }

    /**
     * Increment the number of subscribed users
     *
     * @return bool
     */
    public static function incrementSubscribed()
    {
        $stats = self::firstOrCreate(['id' => 1], [
            'nbr_users' => 0,
            'nbr_subscribed' => 0,
            'nbr_clubs' => 0,
            'nbr_countries' => 0,
            'nbr_sponsors' => 0,
        ]);
        
        $stats->increment('nbr_subscribed');
        
        return true;
    }

    /**
     * Increment the number of clubs
     *
     * @return bool
     */
    public static function incrementClubs()
    {
        $stats = self::firstOrCreate(['id' => 1], [
            'nbr_users' => 0,
            'nbr_subscribed' => 0,
            'nbr_clubs' => 0,
            'nbr_countries' => 0,
            'nbr_sponsors' => 0,
        ]);
        
        $stats->increment('nbr_clubs');
        
        return true;
    }

    /**
     * Update the number of countries
     *
     * @return bool
     */
    public static function updateCountries()
    {
        $stats = self::firstOrCreate(['id' => 1], [
            'nbr_users' => 0,
            'nbr_subscribed' => 0,
            'nbr_clubs' => 0,
            'nbr_countries' => 0,
            'nbr_sponsors' => 0,
        ]);
        
        // Count unique countries from clubs
        $countriesCount = Club::distinct('country')->whereNotNull('country')->count('country');
        
        $stats->update(['nbr_countries' => $countriesCount]);
        
        return true;
    }

    /**
     * Increment the number of sponsors
     *
     * @return bool
     */
    public static function incrementSponsors()
    {
        $stats = self::firstOrCreate(['id' => 1], [
            'nbr_users' => 0,
            'nbr_subscribed' => 0,
            'nbr_clubs' => 0,
            'nbr_countries' => 0,
            'nbr_sponsors' => 0,
        ]);
        
        $stats->increment('nbr_sponsors');
        
        return true;
    }
}
