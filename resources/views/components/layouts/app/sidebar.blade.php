<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head')
    </head>
    <body class="min-h-screen bg-white dark:bg-zinc-800">
        <flux:sidebar sticky stashable class="border-r border-zinc-200 bg-zinc-50 dark:border-zinc-700 dark:bg-zinc-900">
            <flux:sidebar.toggle class="lg:hidden" icon="x-mark" />

            <a href="{{ route('dashboard') }}" class="me-5 flex items-center space-x-2 rtl:space-x-reverse font-semibold text-zinc-600 dark:text-zinc-100">
                <x-app-logo /> Padel Club <br> Dashboard
            </a>

            <flux:navlist variant="outline">
                @if(Auth::guard('admin')->check())
                    <!-- Admin Navigation -->
                    <flux:navlist.group :heading="__('Platform')" class="grid">
                        <flux:navlist.item icon="home" :href="route('dashboard')" :current="request()->routeIs('dashboard')">{{ __('Dashboard') }}</flux:navlist.item>
                        <flux:navlist.item icon="building-storefront" class="mt-2" :href="route('clubs.index')" :current="request()->routeIs('clubs.*')" wire:navigate>{{ __('Clubs Management') }}</flux:navlist.item>
                        <flux:navlist.item icon="users" class="mt-2" :href="route('players.index')" :current="request()->routeIs('players.*')" wire:navigate>{{ __('Players Management') }}</flux:navlist.item>
                        <flux:navlist.item icon="video-camera" class="mt-2" :href="route('cameras.index')" :current="request()->routeIs('cameras.*')" wire:navigate>{{ __('Cameras Management') }}</flux:navlist.item>
                        <flux:navlist.item icon="briefcase" class="mt-2" :href="route('commercials.index')" :current="request()->routeIs('commercials.*')" wire:navigate>{{ __('Commercials Management') }}</flux:navlist.item>
                        <flux:navlist.item icon="currency-dollar" class="mt-2" :href="route('invoicing.index')" :current="request()->routeIs('invoicing.index')" wire:navigate>{{ __('Club Balance & Invoicing') }}</flux:navlist.item>
                        <flux:navlist.item icon="banknotes" class="mt-2" :href="route('commercial-invoicing.index')" :current="request()->routeIs('commercial-invoicing.index')" wire:navigate>{{ __('Commercials Payments') }}</flux:navlist.item>
                        <flux:navlist.item icon="document-currency-euro" class="mt-2" :href="route('invoicing.status')" :current="request()->routeIs('invoicing.status')" wire:navigate>{{ __('Invoices Status') }}</flux:navlist.item>
                    </flux:navlist.group>
                @elseif(Auth::guard('club_web')->check())
                    <!-- Club Navigation -->
                    <flux:navlist.group :heading="__('Club Dashboard')" class="grid">
                        <flux:navlist.item icon="currency-dollar" :href="route('club.balance-details')" :current="request()->routeIs('club.balance-details')" wire:navigate>{{ __('Balance Details') }}</flux:navlist.item>
                    </flux:navlist.group>
                @endif
            </flux:navlist>

            <flux:spacer />



            <!-- Desktop User Menu -->
            <flux:dropdown position="bottom" align="start">
                @if(Auth::guard('admin')->check())
                    <flux:profile
                        :name="auth('admin')->user()->name"
                        :initials="auth('admin')->user()->initials()"
                        icon-trailing="chevrons-up-down"
                    />
                @elseif(Auth::guard('club_web')->check())
                    <flux:profile
                        :name="auth('club_web')->user()->club_name"
                        :initials="strtoupper(substr(auth('club_web')->user()->club_name, 0, 2))"
                        icon-trailing="chevrons-up-down"
                    />
                @endif

                <flux:menu class="w-[220px]">
                    <flux:menu.radio.group>
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                                <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <span
                                        class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white"
                                    >
                                        @if(Auth::guard('admin')->check())
                                            {{ auth('admin')->user()->initials() }}
                                        @elseif(Auth::guard('club_web')->check())
                                            {{ strtoupper(substr(auth('club_web')->user()->club_name, 0, 2)) }}
                                        @endif
                                    </span>
                                </span>

                                <div class="grid flex-1 text-start text-sm leading-tight">
                                    @if(Auth::guard('admin')->check())
                                        <span class="truncate font-semibold">{{ auth('admin')->user()->name }}</span>
                                        <span class="truncate text-xs">{{ auth('admin')->user()->email }}</span>
                                    @elseif(Auth::guard('club_web')->check())
                                        <span class="truncate font-semibold">{{ auth('club_web')->user()->club_name }}</span>
                                        <span class="truncate text-xs">{{ auth('club_web')->user()->email }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    @if(Auth::guard('admin')->check())
                        <flux:menu.radio.group>
                            <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                        </flux:menu.radio.group>

                        <flux:menu.separator />

                        <form method="POST" action="{{ route('logout') }}" class="w-full">
                            @csrf
                            <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                                {{ __('Log Out') }}
                            </flux:menu.item>
                        </form>
                    @elseif(Auth::guard('club_web')->check())
                        <form method="POST" action="{{ route('club.logout') }}" class="w-full">
                            @csrf
                            <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                                {{ __('Log Out') }}
                            </flux:menu.item>
                        </form>
                    @endif
                </flux:menu>
            </flux:dropdown>
        </flux:sidebar>

        <!-- Mobile User Menu -->
        <flux:header class="lg:hidden">
            <flux:sidebar.toggle class="lg:hidden" icon="bars-2" inset="left" />

            <flux:spacer />

            <flux:dropdown position="top" align="end">
                @if(Auth::guard('admin')->check())
                    <flux:profile
                        :initials="auth('admin')->user()->initials()"
                        icon-trailing="chevron-down"
                    />
                @elseif(Auth::guard('club_web')->check())
                    <flux:profile
                        :initials="strtoupper(substr(auth('club_web')->user()->club_name, 0, 2))"
                        icon-trailing="chevron-down"
                    />
                @endif

                <flux:menu>
                    <flux:menu.radio.group>
                        <div class="p-0 text-sm font-normal">
                            <div class="flex items-center gap-2 px-1 py-1.5 text-start text-sm">
                                <span class="relative flex h-8 w-8 shrink-0 overflow-hidden rounded-lg">
                                    <span
                                        class="flex h-full w-full items-center justify-center rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white"
                                    >
                                        @if(Auth::guard('admin')->check())
                                            {{ auth('admin')->user()->initials() }}
                                        @elseif(Auth::guard('club_web')->check())
                                            {{ strtoupper(substr(auth('club_web')->user()->club_name, 0, 2)) }}
                                        @endif
                                    </span>
                                </span>

                                <div class="grid flex-1 text-start text-sm leading-tight">
                                    @if(Auth::guard('admin')->check())
                                        <span class="truncate font-semibold">{{ auth('admin')->user()->name }}</span>
                                        <span class="truncate text-xs">{{ auth('admin')->user()->email }}</span>
                                    @elseif(Auth::guard('club_web')->check())
                                        <span class="truncate font-semibold">{{ auth('club_web')->user()->club_name }}</span>
                                        <span class="truncate text-xs">{{ auth('club_web')->user()->email }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </flux:menu.radio.group>

                    <flux:menu.separator />

                    @if(Auth::guard('admin')->check())
                        <flux:menu.radio.group>
                            <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate>{{ __('Settings') }}</flux:menu.item>
                        </flux:menu.radio.group>

                        <flux:menu.separator />

                        <form method="POST" action="{{ route('logout') }}" class="w-full">
                            @csrf
                            <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                                {{ __('Log Out') }}
                            </flux:menu.item>
                        </form>
                    @elseif(Auth::guard('club_web')->check())
                        <form method="POST" action="{{ route('club.logout') }}" class="w-full">
                            @csrf
                            <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full">
                                {{ __('Log Out') }}
                            </flux:menu.item>
                        </form>
                    @endif
                </flux:menu>
            </flux:dropdown>
        </flux:header>

        <livewire:notification />

        {{ $slot }}

        @fluxScripts
        @stack('scripts')
    </body>
</html>
