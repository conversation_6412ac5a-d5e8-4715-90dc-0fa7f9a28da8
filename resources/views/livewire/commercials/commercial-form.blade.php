<div class="w-full">
    <div class="mb-6">
        <flux:breadcrumbs class="flex-wrap">
            <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item :href="route('commercials.index')" wire:navigate>{{ __('Commercials Management') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item current>{{ $isEdit ? __('Edit Commercial') : __('Add Commercial') }}
            </flux:breadcrumbs.item>
        </flux:breadcrumbs>
    </div>

    <div class="rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
        <form wire:submit="save" class="space-y-6">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Basic Information Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Basic Information') }}</flux:heading>

                    @if ($isEdit)
                        <flux:input wire:model="commercial_id" :label="__('Commercial ID')" disabled readonly />
                    @endif

                    <flux:input wire:model="name" :label="__('Name')" required :error="$errors->first('name')" />

                    <flux:input wire:model="email" :label="__('Email')" type="email" required
                        :error="$errors->first('email')" />

                    <flux:input wire:model="phone" :label="__('Phone')" :error="$errors->first('phone')" />



                    <!-- Password Section -->
                    <div class="mt-6 border-t border-zinc-200 pt-6 dark:border-zinc-700">
                        <flux:heading size="sm">{{ __('Authentication') }}</flux:heading>

                        <div class="mt-4 grid grid-cols-1 gap-6 md:grid-cols-2">
                            <flux:input wire:model="password" :label="__('Password')" type="password"
                                :required="!$isEdit" :error="$errors->first('password')"
                                :helper="$isEdit ? __('Leave blank to keep current password') : ''" />

                            <flux:input wire:model="password_confirmation" :label="__('Confirm Password')"
                                type="password" :required="!$isEdit"
                                :error="$errors->first('password_confirmation')" />
                        </div>
                    </div>


                    <div>
                        <label for="status" class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Status') }}
                        </label>
                        <flux:select wire:model="status" id="status">
                            <option value="1">{{ __('Active') }}</option>
                            <option value="0">{{ __('Inactive') }}</option>
                        </flux:select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Additional Information') }}</flux:heading>

                    <flux:input wire:model="postal_code" :label="__('Postal Code')"
                        :error="$errors->first('postal_code')" />

                    <!-- Country Dropdown -->
                    <div class="relative">
                        <label for="country" class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Country') }}
                        </label>
                        <div class="relative">
                            <input type="text" id="country" wire:model.live.debounce.300ms="countrySearch"
                                wire:click="$set('showCountryDropdown', true)"
                                class="block p-3 w-full rounded-md border-zinc-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-zinc-600 dark:bg-zinc-800 dark:text-white sm:text-sm"
                                placeholder="{{ __('Search country...') }}">
                            @if ($showCountryDropdown && count($countries) > 0)
                                <div
                                    class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-zinc-800 sm:text-sm">
                                    @foreach ($countries as $country)
                                        <div wire:key="country-{{ $country->id }}"
                                            wire:click="selectCountry('{{ $country->name }}')"
                                            class="relative cursor-pointer select-none py-2 pl-3 pr-9 hover:bg-zinc-100 dark:hover:bg-zinc-700">
                                            {{ $country->name }}
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                        @error('country')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <flux:input wire:model="tax_id" :label="__('Tax ID')" :error="$errors->first('tax_id')" />


                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="outline" :href="route('commercials.index')" wire:navigate>
                    {{ __('Cancel') }}
                </flux:button>

                <flux:button variant="primary" type="submit">
                    {{ $isEdit ? __('Update Commercial') : __('Create Commercial') }}
                </flux:button>
            </div>
        </form>
    </div>
</div>
