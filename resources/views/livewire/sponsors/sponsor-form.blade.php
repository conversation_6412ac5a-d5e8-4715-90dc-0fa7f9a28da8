<div class="w-full">
    <div class="mb-6">
        <flux:breadcrumbs class="flex-wrap">
            <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item :href="route('sponsors.index')" wire:navigate>{{ __('Sponsors Management') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item current>{{ $isEdit ? __('Edit Sponsor') : __('Add Sponsor') }}
            </flux:breadcrumbs.item>
        </flux:breadcrumbs>
    </div>

    <div class="rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
        <form wire:submit="save" class="space-y-6">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Basic Information Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Basic Information') }}</flux:heading>

                    @if ($isEdit)
                        <flux:input wire:model="sponsor_id" :label="__('Sponsor ID')" disabled readonly />
                    @endif

                    <flux:input wire:model="company_name" :label="__('Company Name')" required :error="$errors->first('company_name')" />

                    <flux:input wire:model="email" :label="__('Email')" type="email" required
                        :error="$errors->first('email')" />

                    <flux:input wire:model="phone" :label="__('Phone')" :error="$errors->first('phone')" />

                    <flux:input wire:model="contact" :label="__('Contact Person')" :error="$errors->first('contact')" />


                    <div>
                        <label for="commercial_id" class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Commercial') }} <span class="text-red-500">*</span>
                        </label>
                        <flux:select wire:model="commercial_id" id="commercial_id" required>
                            <option value="">{{ __('Select Commercial') }}</option>
                            @foreach($commercials as $commercial)
                                <option value="{{ $commercial->commercial_id }}">{{ $commercial->name }}</option>
                            @endforeach
                        </flux:select>
                        @error('commercial_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="status" class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Status') }}
                        </label>
                        <flux:select wire:model="status" id="status">
                            <option value="1">{{ __('Active') }}</option>
                            <option value="0">{{ __('Inactive') }}</option>
                        </flux:select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Additional Information') }}</flux:heading>

                    <flux:input wire:model="postal_code" :label="__('Postal Code')"
                        :error="$errors->first('postal_code')" />


                    <flux:input wire:model="tax_id" :label="__('Tax ID')" :error="$errors->first('tax_id')" />

                    <!-- Country Search -->
                    <div class="relative">
                        <label for="countrySearch" class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Country') }}
                        </label>
                        <flux:input 
                            wire:model.live="countrySearch" 
                            id="countrySearch"
                            :placeholder="__('Search for a country...')"
                            wire:focus="$set('showCountryDropdown', true)"
                            :error="$errors->first('country')" />
                        
                        @if($showCountryDropdown && count($countries) > 0)
                            <div class="absolute z-10 mt-1 w-full rounded-md border border-zinc-300 bg-white shadow-lg dark:border-zinc-600 dark:bg-zinc-800">
                                <ul class="max-h-60 overflow-auto py-1">
                                    @foreach($countries as $country)
                                        <li wire:click="selectCountry({{ $country->id }})" 
                                            class="cursor-pointer px-3 py-2 hover:bg-zinc-100 dark:hover:bg-zinc-700">
                                            <span class="text-sm text-zinc-900 dark:text-zinc-100">{{ $country->name }}</span>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                    </div>

                    <!-- Logo Upload -->
                    <div>
                        <label for="logo" class="mb-1 block text-sm font-medium text-zinc-700 dark:text-zinc-300">
                            {{ __('Logo') }}
                        </label>
                        <input type="file" wire:model="logo" id="logo" accept="image/*"
                            class="block w-full text-sm text-zinc-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900 dark:file:text-blue-300">
                        @error('logo')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        
                        <!-- Show existing logo if editing -->
                        @if($isEdit && $existingLogo)
                            <div class="mt-2">
                                <p class="text-sm text-zinc-600 dark:text-zinc-400 mb-1">{{ __('Current Logo:') }}</p>
                                <img src="{{ asset('storage/' . $existingLogo) }}" alt="Current Logo" 
                                     class="h-16 w-16 rounded object-cover">
                            </div>
                        @endif
                        
                        <!-- Show preview of new logo -->
                        @if($logo)
                            <div class="mt-2">
                                <p class="text-sm text-zinc-600 dark:text-zinc-400 mb-1">{{ __('New Logo Preview:') }}</p>
                                <img src="{{ $logo->temporaryUrl() }}" alt="Logo Preview" 
                                     class="h-16 w-16 rounded object-cover">
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="outline" :href="route('sponsors.index')" wire:navigate>
                    {{ __('Cancel') }}
                </flux:button>

                <flux:button variant="primary" type="submit">
                    {{ $isEdit ? __('Update Sponsor') : __('Create Sponsor') }}
                </flux:button>
            </div>
        </form>
    </div>
</div>

<script>
    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.querySelector('[x-show="showCountryDropdown"]');
        const input = document.getElementById('countrySearch');
        
        if (dropdown && !dropdown.contains(event.target) && event.target !== input) {
            @this.set('showCountryDropdown', false);
        }
    });
</script>
